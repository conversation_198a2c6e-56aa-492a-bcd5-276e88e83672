<template>
  <div>
    <div class="row-container" style="height: 35px; margin-bottom: -5px; margin-left: 2px; align-items: center; justify-content: space-between; margin-right: auto">
      <div class="row-container">
        <input type="radio" id="page1" name="page_tab" @change="$el.querySelector('#page1_link').click()" style="display: none" value="page1" v-model="radioSelected" />
        <label for="page1" class="basic-font link" :style="{ backgroundColor: radioSelected == 'page1' ? '#066cc5' : '#8CAFCF' }">替換</label>
        <router-link to="/page1" id="page1_link" class="basic-font" style="display: none"> </router-link>
      </div>
      <div class="row-container" style="float: right; margin-top: -3px; margin-right: 5px; height: 24px">
        <button v-if="isNewVersion" class="small" style="margin-right: 5px; font-size: 14px; width: 120px; height: 24px" @click.stop="updateApp">
          <div v-if="isDownloaded"><i class="fa fa-check-circle" aria-hidden="true" style="color: #00ff15; margin-right: 3px"></i>安裝{{ newVersion }}</div>
          <div v-else><i class="fa fa-spinner fa-spin" aria-hidden="true" style="color: white; margin-right: 3px"></i>下載中{{ progress }}</div>
        </button>
        <div class="basic-font" style="margin-right: 5px; font-size: 14px">目前版本：{{ $store.getters.appVersion }}</div>
      </div>
    </div>
    <router-view style="height: calc(100vh - 30px)" />
    <div id="alertmenu" class="modal">
      <div class="modal-content2" style="height: 180px; width: 240px; border-radius: 5px">
        <span class="modal-text basic-font"
          ><i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: #ffd30a"></i> 注意 <i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: #ffd30a"></i
        ></span>
        <div style="display: table; height: 45px; width: 80%; overflow: hidden; margin-top: 10px">
          <div style="display: table-cell; vertical-align: middle">
            <div class="basic-font" style="text-align: center; font-weight: bold">
              {{ errorText }}
            </div>
          </div>
        </div>
        <button class="medium" style="margin-top: 15px" @click.stop="closeAlert">確認</button>
      </div>
    </div>
  </div>
</template>

<style>
html,
body {
  overflow: hidden !important;
}

/* .wrapper {
  display: grid;
  grid-template-rows: 30px 1fr;
} */

.link {
  font-size: 14px;
  min-width: 50px;
  border-radius: 5px;
  color: white;
  margin: 0 2px;
  padding: 3px 15px;
  padding-bottom: 8px;
  /* background-color: #094479; */
  text-decoration: none;
  transition: background-color 0.25s;
}
</style>

<script>
window.$ = window.jQuery = require("jquery");
import "@/assets/style.css";
// const electron = require("electron");
// const app = electron.app || electron.remote.app;

// const { remote } = require("electron");

export default {
  name: "App",
  // methods: {
  //   showVersionInfo: function() {
  //     // var a = window.registerFuncs.currentVersion();
  //     return 123;
  //   },
  // },
  created() {
    console.log("App component created");
  },
  methods: {
    closeAlert() {
      this.$el.querySelector("#alertmenu").style.display = "none";
      if (this.isend) {
        switch (this.radioSelected) {
          case "page1":
            this.$bus.$emit("page1-end");
            break;
        }
      }
    },
    updateApp() {
      if (this.isDownloaded) {
        window.registerFuncs.updateApp();
      }
    },
  },
  created() {
    console.log("App component created - setting up event listeners");
    this.$bus.$on("show-warn", (warntext, isend) => {
      this.isend = isend;
      this.errorText = warntext;
      this.$el.querySelector("#alertmenu").style.display = "block";
    });
  },
  mounted() {
    console.log("App component mounted - setting up message listeners");
    window.addEventListener("message", (event) => {
      switch (event.data.type) {
        case "update-available":
          this.isNewVersion = true;
          this.newVersion = event.data.newVersion;
          break;
        case "download-progress":
          this.progress = event.data.progress;
          break;
        case "update-downloaded":
          this.isDownloaded = true;
          break;
      }
    });
  },
  data() {
    return {
      isend: false,
      // currentAppVersion: "",
      radioSelected: "page1",
      newVersion: "0.0.0",
      isNewVersion: false,
      isDownloaded: false,
      progress: "0%",
      errorText: "我我我我我我我我",
    };
  },
  beforeDestroy() {
    this.$bus.$off("show-warn");
  },
};
</script>
