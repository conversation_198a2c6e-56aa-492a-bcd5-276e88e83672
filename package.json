{"name": "image_exchanger", "version": "1.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:build": "vue-cli-service electron:build", "electron:serve": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild -f -w yourmodule"}, "main": "background.js", "dependencies": {"core-js": "^3.43.0", "electron-updater": "^6.6.2", "font-awesome": "^4.7.0", "fs-extra": "^11.3.0", "image-size": "^1.0.2", "jquery": "^3.7.1", "node-processlist": "^1.0.2", "pixelmatch": "^7.1.0", "ps-node": "^0.1.6", "sharp": "^0.34.2", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuetify": "^2.4.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-service": "^5.0.8", "electron": "^37.1.0", "eslint": "^9.29.0", "eslint-plugin-vue": "^10.2.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "vue-cli-plugin-electron-builder": "^2.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=22.4.0", "npm": ">=10.0.0"}}