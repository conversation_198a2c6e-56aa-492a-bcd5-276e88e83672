import Vue from "vue";
import Vuex from "vuex";
import App from "./App.vue";
import vuetify from "./plugins/vuetify";
import router from "./router";
import "font-awesome/css/font-awesome.min.css";
// import "@/assets/style.css";

console.log("Starting Vue app initialization...");

Vue.config.productionTip = false;
Vue.prototype.$bus = new Vue();
// Vue.component("v-jstree", VJstree);
Vue.use(Vuex);
// Vue.use(vcolorpicker);

console.log("Creating Vuex store...");
const store = new Vuex.Store({
  state: {
    packageVersion: process.env.VUE_APP_VERSION || "1.0.0",
  },
  getters: {
    appVersion: (state) => {
      return state.packageVersion;
    },
  },
});

console.log("Creating Vue instance...");
try {
  new Vue({
    store: store,
    router,
    components: {},
    vuetify,
    render: (h) => h(App),
  }).$mount("#app");
  console.log("Vue app mounted successfully!");
} catch (error) {
  console.error("Error mounting Vue app:", error);
}
