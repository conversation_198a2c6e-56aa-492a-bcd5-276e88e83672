"use strict";

import { app, protocol, BrowserWindow, ipc<PERSON>ain, dialog, shell } from "electron";
import { createProtocol } from "vue-cli-plugin-electron-builder/lib";
// const { ipcMain } = require("electron");
// import installExtension, { VUEJS_DEVTOOLS } from "electron-devtools-installer";
const updater = require('./update.js');
const isDevelopment = process.env.NODE_ENV !== "production";
const path = require("path");
const fs = require("fs-extra");
const sizeOf = require("image-size");
const processlist = require("node-processlist");
const ImageSimilarityCalculator = require("./similarity");
const ps = require("ps-node");
// Scheme must be registered before the app is ready
protocol.registerSchemesAsPrivileged([{ scheme: "app", privileges: { secure: true, standard: true } }]);
// const { BrowserView } = require("electron");

async function createWindow() {
  // Create the browser window.
  const preloadPath = path.join(__dirname, "preload.js");
  console.log("Preload script path:", preloadPath);
  console.log("Preload script exists:", require('fs').existsSync(preloadPath));

  const win = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 500,
    minHeight: 300,
    webPreferences: {
      // Use pluginOptions.nodeIntegration, leave this alone
      // See nklayman.github.io/vue-cli-plugin-electron-builder/guide/security.html#node-integration for more info
      // nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      // preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
      // preload: path.join(__dirname, "preload.js"),
      preload: preloadPath,
      webviewTag: true,
      nodeIntegration: true,  // 啟用 Node.js 集成以支持文件拖曳
      contextIsolation: false, // 禁用上下文隔離以允許訪問文件路徑
      webSecurity: false,
      enableRemoteModule: true // 啟用遠程模塊（如果需要）
    },
  });

  // let template = [
  //   {
  //     label: "導出專案",
  //     click() {
  //       changePage("/index.html");
  //     },
  //   },
  //   {
  //     label: "導入專案",
  //     click() {
  //       changePage("/import.html");
  //     },
  //   },
  //   {
  //     label: "轉換檔案",
  //     click() {
  //       changePage("/swap.html");
  //     },
  //   },
  // ];
  // // 加载菜单
  // let m = Menu.buildFromTemplate(template);
  // Menu.setApplicationMenu(m);
  updater.init(win);
  win.setMenuBarVisibility(false); //隱藏工作列
  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL + "/index.html");
    if (!process.env.IS_TEST) {
      win.webContents.openDevTools();
    }
  } else {
    createProtocol("app");
    win.loadURL("app://./index.html");
    win.once('ready-to-show', () => {
      updater.checkUpdate();
    });
  }
  win.webContents.openDevTools();
  // win.webContents.openDevTools();
}
// function changePage(page) {
//   if (process.env.WEBPACK_DEV_SERVER_URL) {
//     BrowserWindow.getFocusedWindow().loadURL(process.env.WEBPACK_DEV_SERVER_URL + page);
//   } else {
//     createProtocol("app");
//     win.loadURL("app://." + page);
//   }
//   // console.log(`${process.env.WEBPACK_DEV_SERVER_URL}/#${data.route}`);
//   // ipcMain.send("TEST_IPC", index);
// }
// Quit when all windows are closed.
app.on("window-all-closed", () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", async () => {
  createWindow();
  ipcMain.on("startUpdate", (e, arg) => {
    updater.quitAndInstall();
  });

  // Handle dialog operations
  ipcMain.handle("select-folder", async () => {
    const result = await dialog.showOpenDialog({
      properties: ["openDirectory"]
    });
    return result.canceled ? null : result.filePaths[0];
  });

  ipcMain.handle("select-file", async () => {
    const result = await dialog.showOpenDialog({
      properties: ["openFile"]
    });
    return result.canceled ? null : result.filePaths[0];
  });

  ipcMain.handle("save-dropped-file", async (event, fileName, fileBuffer) => {
    try {
      const os = require('os');
      const tempDir = os.tmpdir();
      const tempFilePath = path.join(tempDir, `dropped_${Date.now()}_${fileName}`);

      // 將 Uint8Array 轉換為 Buffer
      const buffer = Buffer.from(fileBuffer);

      // 保存文件到臨時目錄
      await fs.promises.writeFile(tempFilePath, buffer);

      console.log("保存拖曳文件到:", tempFilePath);
      return tempFilePath;
    } catch (error) {
      console.error("保存拖曳文件時出錯:", error);
      return null;
    }
  });

  ipcMain.handle("get-app-version", () => {
    return app.getVersion();
  });

  // File system operations
  ipcMain.handle("append-text", (event, text) => {
    const newPath = path.resolve(__dirname, "the-text.txt");
    fs.writeFileSync(newPath, text + "\n", {
      encoding: "utf8",
      flag: "a",
    });
  });

  ipcMain.handle("get-filename", (event, temppath) => {
    return path.parse(temppath).base;
  });

  ipcMain.handle("studio-paths", (event, src, p) => {
    var p2 = path.dirname(src);
    return path.join(p2, "cocosstudio", p);
  });

  ipcMain.handle("compare-ext", (event, temppath, ext) => {
    return path.parse(temppath).ext == ext;
  });

  ipcMain.handle("is-directory", (event, temppath) => {
    return fs.lstatSync(temppath).isDirectory();
  });

  ipcMain.handle("read-xml", (event, temppath) => {
    return fs.readFileSync(temppath, "utf-8");
  });

  ipcMain.handle("open-folder", (event, p) => {
    if (path.parse(p).ext != "") {
      p = path.dirname(p);
    }
    shell.openPath(p);
  });

  ipcMain.handle("get-img-size", (event, p) => {
    return sizeOf(p);
  });

  ipcMain.handle("get-dir-length", (event, dir) => {
    if (dir) {
      return dir.split(path.sep).length;
    } else {
      return 0;
    }
  });

  ipcMain.handle("exchange-image", async (event, desPath, newPath) => {
    await fs.promises.unlink(desPath);
    let filedata = await fs.promises.readFile(newPath);
    await fs.promises.writeFile(desPath, filedata);
  });

  ipcMain.handle("is-child-of", (event, parent, child) => {
    if (parent == child) {
      return true;
    }
    let relative = path.relative(parent, child);
    return relative && !relative.startsWith('..') && !path.isAbsolute(relative);
  });

  ipcMain.handle("get-folder-files", (event, folderPath) => {
    function getFolderFilesRecursive(folderPath) {
      let files = fs.readdirSync(folderPath);
      let fileList = [];
      files.forEach(function (filename) {
        let ext = path.parse(filename).ext;
        if (fs.statSync(folderPath + "/" + filename).isDirectory()) {
          fileList = fileList.concat(getFolderFilesRecursive(path.join(folderPath, filename)));
        } else if (ext == ".png" || ext == ".jpg") {
          let foldername = path.parse(folderPath).name;
          fileList.push({ foldername: foldername, dirname: folderPath, path: path.join(folderPath, filename) });
        }
      });
      return fileList;
    }
    return getFolderFilesRecursive(folderPath);
  });

  ipcMain.handle("is-source", (event, mainpath, subpath) => {
    var dirpath = path.dirname(subpath);
    var p = fs.readdirSync(dirpath).find((name) => path.parse(name).name == "_exportdetail");
    if (p) {
      var filepath = path.join(dirpath, p);
      var file = fs.readFileSync(filepath, "utf-8");
      try {
        JSON.parse(file);
      } catch (e) {
        return false;
      }
      var obj = JSON.parse(file);
      if (obj.srcname == path.parse(mainpath).name) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  });
});

// 圖片相似度比較
const similarityCalculator = new ImageSimilarityCalculator();

ipcMain.handle('compare-image-similarity', async (event, imagePath1, imagePath2, customWeights) => {
  try {
    const result = await similarityCalculator.calculateSimilarity(imagePath1, imagePath2, customWeights);

    // 返回 0-1 之間的相似度值（與原來的接口保持一致）
    const similarity = result.overall_similarity / 100;

    console.log(`圖片相似度比較: ${path.basename(imagePath1)} vs ${path.basename(imagePath2)} = ${result.overall_similarity.toFixed(1)}%`);
    console.log(`  - 直方圖: ${result.histogram_similarity.toFixed(1)}%`);
    console.log(`  - 像素: ${result.pixel_similarity.toFixed(1)}%`);
    console.log(`  - 結構: ${result.structural_similarity.toFixed(1)}%`);

    return Math.max(0, Math.min(1, similarity));
  } catch (error) {
    console.error(`Error comparing images ${imagePath1} vs ${imagePath2}:`, error);
    return 0;
  }
});

// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === "win32") {
    process.on("message", (data) => {
      if (data === "graceful-exit") {
        app.quit();
      }
    });
  } else {
    process.on("SIGTERM", () => {
      app.quit();
    });
  }
}
