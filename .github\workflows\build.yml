name: Build

permissions:
  contents: write
  packages: write

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  release:
    name: build and release electron app
    runs-on: self-hosted

    steps:
      - name: Check out git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 16.13.0

      - name: Install Dependencies
        run: npm install

      - name: Build Electron App
        run: npm run electron:build

      - name: Cleanup Artifacts for Windows(1)
        run: |
          npx rimraf "dist_electron/win-unpacked"

      - name: Cleanup Artifacts for Windows(2)
        run: |
          npx rimraf "dist_electron/bundled"

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          path: dist_electron

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          files: "dist_electron/**"
        env:
          token: ${{ secrets.ELECTRON_TOKEN }}
