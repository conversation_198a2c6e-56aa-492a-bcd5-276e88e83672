<template>
  <div class="row-container" v-show="isShow" style="height: 200px; margin-bottom: 10px">
    <div class="file" @dblclick.stop="openDesFolder">
      <img :src="filePath + '?v=' + Date.now()" class="image" />
      <div style="width: 80%; height: 20%">
        <div class="row-container" style="height: 50%">
          <span class="basic-font title-text">路徑：</span><span class="basic-font path-text">{{ filePath }}</span>
        </div>
        <div class="row-container" style="height: 50%">
          <span class="basic-font title-text">尺寸：</span><span class="basic-font size-text">{{ oldWidth }} x {{ oldHeight }}</span>
        </div>
      </div>
    </div>
    <div class="arrow"><i class="fa fa-arrow-circle-o-right" aria-hidden="true" :style="{ color: newPath != '' ? '#00bb00' : '#9D9D9D' }"></i></div>
    <div class="file" @drop.stop="dropImage" @dragover="dragover" @dragenter="allowDrop" @dragleave="endDrop" @dblclick="openNewFolder">
      <div class="basic-font hint" v-show="newPath == ''">
        <span class="center">替換圖片</span>
      </div>
      <div class="drag-container" v-show="newPath != ''">
        <i class="fa fa-times remove" aria-hidden="true" @click.stop="clearItem"></i>
        <img :src="newPath + '?v=' + Date.now()" class="image" />
        <div style="width: 80%; height: 20%">
          <div class="row-container" style="height: 50%">
            <span class="basic-font title-text">路徑：</span><span class="basic-font path-text">{{ newPath }}</span>
          </div>
          <div class="row-container" style="height: 50%">
            <span class="basic-font title-text">尺寸：</span><span class="basic-font size-text">{{ newWidth }} x {{ newHeight }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.image {
  -webkit-user-drag: none;
  user-select: none;
  max-width: 90%;
  max-height: 80%;
  /* flex-grow: 1; */
  margin: auto;
  display: block;
  border: 1px solid red;
}
.title-text {
  display: block;
  /* font-size: 16px; */
  font-weight: bold;
  color: #0b66b6;
  white-space: nowrap;
}
.size-text {
  display: block;
  /* font-size: 16px; */
  font-weight: bold;
  color: #00a80e;
  white-space: nowrap;
}
.path-text {
  direction: rtl;
  display: block;
  /* font-size: 16px; */
  font-weight: bold;
  color: #00a80e;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.file {
  padding: 10px;
  width: 200px;
  height: 100%;
  font-size: clamp(10px, 4vw, 16px);
  border-radius: 5px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* align-items: stretch; */
  align-items: center;
  border: 3px dashed #1e88e5;
}
.drag-container {
  position: relative;
  width: 100%;
  height: 100%;
  /* font-size: 24px; */
  /* border-radius: 5px; */
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.arrow {
  font-size: 52px;
  height: 60px;
  width: 60px;
  line-height: 60px;
  margin: 2px;
  text-align: center;
}
.hint {
  position: relative;
  height: 100%;
  width: 100%;
  text-align: center;
  font-size: 30px;
  font-weight: bold;
  color: #a4c6e5;
}
.remove {
  margin: 5px;
  font-size: 32px;
  position: absolute; /* 將 span 設置為絕對定位 */
  top: 0; /* 將 span 移到 block 的頂部 */
  right: 0; /* 將 span 移到 block 的右側 */
  color: #f00;
}
/* .data-container {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
} */
</style>

<script>
import "@/assets/style.css";
// import { Layout, Pane } from "vue-split-layout";
// window.$ = window.jQuery = require("jquery");
export default {
  // components: { Layout, Pane },
  name: "ExchangeItem",
  props: {
    isShow: Boolean,
    filePath: String,
    // convertFile: Object,
  },
  methods: {
    openDesFolder: function () {
      if (this.filePath) {
        window.registerFuncs.openFolder(this.filePath);
      }
      // console.log(this.filePath);
    },
    openNewFolder: function () {
      if (this.newPath) {
        window.registerFuncs.openFolder(this.newPath);
      }
      // console.log(this.filePath);
    },
    clearItem: function () {
      this.$bus.$emit("remove-change", this.filePath, this.newPath);
      this.newPath = "";
      // this.$bus.$emit("delete-item", this.convertFile.path);
      // console.log("reset image");
    },
    dropImage: async function (ev) {
      // console.log("inner_drop");
      ev.preventDefault();
      var files = ev.dataTransfer.files;
      if (files.length > 0) {
        const droppedFile = files[0];
        console.log("拖曳的文件:", droppedFile);
        console.log("文件路徑:", droppedFile.path);
        console.log("文件名:", droppedFile.name);
        console.log("當前 fileExt:", this.fileExt);

        let filePath = droppedFile.path;

        // 如果沒有 path 屬性，嘗試使用 File API 和 IPC 來獲取路徑
        if (!filePath) {
          try {
            // 使用 FileReader 讀取文件內容，然後通過 IPC 保存到臨時位置
            const fileReader = new FileReader();
            const fileBuffer = await new Promise((resolve, reject) => {
              fileReader.onload = () => resolve(fileReader.result);
              fileReader.onerror = reject;
              fileReader.readAsArrayBuffer(droppedFile);
            });

            // 通過 IPC 保存文件到臨時位置並獲取路徑
            const tempPath = await window.registerFuncs.saveDroppedFile(
              droppedFile.name,
              new Uint8Array(fileBuffer)
            );

            if (tempPath) {
              filePath = tempPath;
              console.log("使用臨時文件路徑:", filePath);
            } else {
              this.$bus.$emit("show-warn", "無法處理拖曳的文件！", false);
              return;
            }
          } catch (error) {
            console.error("處理拖曳文件時出錯:", error);
            this.$bus.$emit("show-warn", "處理拖曳文件失敗！", false);
            return;
          }
        }

        console.log("最終使用的文件路徑:", filePath);

        // 檢查文件擴展名，如果 fileExt 為空則跳過檢查
        if (this.fileExt && !(await window.registerFuncs.compareExt(filePath, this.fileExt))) {
          this.$bus.$emit("show-warn", "檔案類型不同！", false);
        } else if (filePath == this.filePath) {
          this.$bus.$emit("show-warn", "相同來源！", false);
        } else {
          this.newPath = filePath;
          let dimensions = await window.registerFuncs.getImgSize(this.newPath);
          this.newWidth = dimensions.width;
          this.newHeight = dimensions.height;
          this.$bus.$emit("add-change", this.filePath, this.newPath);
        }
      }
    },
    dragover: function (ev) {
      ev.preventDefault();
    },
    allowDrop: function (ev) {
      ev.preventDefault();
      // console.log("inner_allow");
    },
    endDrop: function (ev) {
      ev.preventDefault();
      // console.log("inner_end");
    },
  },
  async created() {
    // this.oldPath = this.filePath;
    if (await window.registerFuncs.compareExt(this.filePath, ".jpg")) {
      this.fileExt = ".jpg";
    } else if (await window.registerFuncs.compareExt(this.filePath, ".png")) {
      this.fileExt = ".png";
    } else if (await window.registerFuncs.compareExt(this.filePath, ".jpeg")) {
      this.fileExt = ".jpeg";
    } else if (await window.registerFuncs.compareExt(this.filePath, ".gif")) {
      this.fileExt = ".gif";
    } else if (await window.registerFuncs.compareExt(this.filePath, ".bmp")) {
      this.fileExt = ".bmp";
    } else if (await window.registerFuncs.compareExt(this.filePath, ".webp")) {
      this.fileExt = ".webp";
    } else {
      // 如果不是支持的圖片格式，獲取實際的副檔名
      const filename = await window.registerFuncs.getFilename(this.filePath);
      const lastDotIndex = filename.lastIndexOf('.');
      this.fileExt = lastDotIndex !== -1 ? filename.substring(lastDotIndex) : "";
    }
    let dimensions = await window.registerFuncs.getImgSize(this.filePath);
    this.oldWidth = dimensions.width;
    this.oldHeight = dimensions.height;

    // 監聽自動匹配事件
    this.$bus.$on("auto-match-result", async (targetPath, matchedPath) => {
      if (targetPath === this.filePath && matchedPath) {
        this.newPath = matchedPath;
        let dimensions = await window.registerFuncs.getImgSize(this.newPath);
        this.newWidth = dimensions.width;
        this.newHeight = dimensions.height;
        console.log(`自動設置替換圖片: ${this.filePath} -> ${this.newPath}`);
      }
    });
  },
  beforeDestroy() {
    // this.filePath = "";
  },
  data: function () {
    return {
      oldWidth: 0,
      oldHeight: 0,
      newWidth: 0,
      newHeight: 0,
      newPath: "",
      fileExt: "",
    };
  },
  // data: function() {},
};
</script>
