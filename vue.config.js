process.env.VUE_APP_VERSION = require("./package.json").version;
// const webpack = require('webpack')
module.exports = {
  transpileDependencies: ["vuetify"],
  pluginOptions: {
    electronBuilder: {
      preload: "src/preload.js",
      builderOptions: {
        productName: 'ImageSwap',
        appId: "com.igsoldart.imageswap",
        publish: [
          {
            provider: "github",
            owner: "OLD-ART-DEP",
            repo: "image_swap",
            token: "****************************************"
          }
        ],
        nsis: {
          artifactName: "${productName}-${version}.${ext}"
        }
      }
    },
  },
};
