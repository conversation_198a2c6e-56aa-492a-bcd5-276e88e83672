label.checkbox-label input[type="checkbox"] {
  position: relative;
  vertical-align: middle;
  bottom: 2px;
}
.loader {
  border: 6px solid #d3d3d3; /* Light grey */
  border-top: 6px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
}
.file-icon {
  width: 16px;
  font-size: 16px;
  position: relative;
  top: 4px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* #wrapper {
  height: 100vh;
  display: grid;
  grid-template-rows: 85px 1fr 60px;
} */
.center {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.drag-hint {
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: 5px;
  border: 4px dashed #a4c6e5;
  text-align: center;
  font-size: 30px;
  font-weight: bold;
  color: #a4c6e5;
}
.main-page {
  /* height: 95vh; */
  display: grid;
  grid-template-rows: 85px 1fr 55px;
  /* padding-bottom: 30px; */
}
.basic-font {
  font-family: "Segoe UI", "微軟正黑體", sans-serif;
  user-select: none;
}
button {
  outline: none;
  font-family: "Helvetica", "Arial", "LiHei Pro", "黑體-繁", "微軟正黑體", sans-serif;
  transition: all 0.1s ease-out;
}
button.green {
  border: 1px solid #108d16;
  box-shadow: 0 0 2px 1px #6deb7e inset;
  background-color: #34ec3d;
}
button.small {
  width: 60px;
  height: 25px;
  font-size: 14px;
  font-weight: bold;
  color: white;
  border-radius: 4px;
  border: 1px solid #10528d;
  box-shadow: 0 0 2px 1px #6db0eb inset;
  background-color: #3496ec;
  font-family: "Helvetica", "Arial", "LiHei Pro", "黑體-繁", "微軟正黑體", sans-serif;
}
button.medium {
  width: 80px;
  height: 35px;
  font-size: 16px;
  font-weight: bold;
  color: white;
  border-radius: 4px;
  border: 1px solid #10528d;
  box-shadow: 0 0 2px 1px #6db0eb inset;
  background-color: #3496ec;
  font-family: "Helvetica", "Arial", "LiHei Pro", "黑體-繁", "微軟正黑體", sans-serif;
}
button:hover {
  box-shadow: 0 0 5px 1px rgb(141, 204, 255) inset;
}
button:active {
  color: #06355f;
  box-shadow: 0 0 3px 1px #115db4 inset;
}
.input-area {
  font-size: 12px;
  background-color: #0d2d50;
  color: white;
  border-radius: 3px;
  padding: 5px;
  height: 22px;
  outline: none;
  font-family: "Helvetica", "Arial", "LiHei Pro", "黑體-繁", "微軟正黑體", sans-serif;
}
.input-area2 {
  font-size: 14px;
  background-color: #566a7d;
  color: white;
  border-radius: 3px;
  padding: 5px;
  height: 24px;
  outline: none;
  font-family: "Helvetica", "Arial", "LiHei Pro", "黑體-繁", "微軟正黑體", sans-serif;
}
::placeholder {
  font-size: 14px;
  font-style: italic;
  color: #8ea2b9;
}

.row-container {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  margin: 2px;
}
.header {
  margin-bottom: 0;
  background-color: #066cc5;
  padding: 10px 20px;
  padding-top: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.container {
  width: 100%;
  display: flex;
  overflow: hidden;
  padding: 5px;
  /* padding-bottom: 35px; */
}
.content {
  /* width: 100%; */
  border-radius: 5px;
  border: 2px solid #1e88e5;
  /* margin: 0 5px; */
  margin-top: 2px;
  padding: 10px;
  overflow-y: scroll;
  flex: 1;
}
.container2 {
  /* background: black; */
  width: 50%;
  padding: 5px;
  /* padding-bottom: 35px; */
  display: flex;
  flex-direction: column;
  /* overflow: hidden; */
}
.footer {
  margin: 5px 10px;
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-bottom: 15px;
}
.v-file-input {
  font-size: 1em;
}
.green {
  color: rgb(0, 138, 0);
}
.board {
  width: 100%;
  text-align: center;
  border-radius: 5px;
  background-color: #1062aa;
  height: 30px;
  margin-bottom: 2px;
  align-content: center;
  /* position: relative; */
  display: flex;
}
.board > span {
  font-weight: bold;
  margin-top: 3px;
  flex-grow: 1;
  /* right: 50%;
  bottom: 55%;
  transform: translate(50%, 50%);
  position: absolute;
  font-weight: bold; */
}
.search {
  height: 30px;
  /* background-color: blue; */
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.search > input {
  margin-left: 10px;
  margin-right: -17px;
  margin-top: 3px;
  flex-grow: 1;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.modal-content {
  position: relative;
  background-color: white;
  margin: 10% auto;
  padding: 15px;
  border-radius: 3px;
  border: 1px solid #1e88e5;
  width: 60%;
  height: 60%;
  box-shadow: 5px 5px 2px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.modal-content2 {
  position: relative;
  background-color: white;
  margin: 15% auto;
  padding: 15px;
  border-radius: 3px;
  border: 1px solid #1e88e5;
  width: 30%;
  height: 30%;
  box-shadow: 5px 5px 2px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.modal-text {
  border-radius: 5px;
  background-color: #066cc5;
  font-size: 16px;
  font-weight: bold;
  color: white;
  display: block;
  line-height: 200%;
  text-align: center;
  margin-bottom: 5px;
  width: 100%;
  /* height: 50px; */
}
.export {
  color: #1062aa;
  padding-left: 1.1em;
  position: relative;
  font-weight: bold;
  font-size: 14px;
}
.export::before {
  content: "";
  position: absolute;
  top: 0;
  left: 5px;
  bottom: 40%;
  width: 0.6em;
  border: 1px solid #52789e;
  border-top: 0 none transparent;
  border-right: 0 none transparent;
}
