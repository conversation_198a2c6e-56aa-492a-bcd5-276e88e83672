const { autoUpdater } = require('electron-updater');
var mainWin = null;

autoUpdater.autoDownload = true;
autoUpdater.autoInstallOnAppQuit = false;

function init(win) {
    mainWin = win;
}

function checkUpdate() {
    mainWin.webContents.send('msg-to-preload', '更新程序開始');
    autoUpdater.checkForUpdates();
};

function quitAndInstall() {
    mainWin.webContents.send('msg-to-preload', '關閉並開始更新');
    autoUpdater.quitAndInstall();
}

autoUpdater.on('update-available', (info) => {
    mainWin.webContents.send('msg-to-preload', '偵測到新版本 :' + info.version);
    mainWin.webContents.send('update-available', info.version);

});
autoUpdater.on('download-progress', (prog) => {
    // mainWin.webContents.send('msg-to-preload', '下載中');
    mainWin.webContents.send('download-progress', prog.percent.toFixed(1) + '%');
});
autoUpdater.on('update-downloaded', (info) => {
    mainWin.webContents.send('msg-to-preload', '新版本下載完成 :' + info.version);
    mainWin.webContents.send('update-downloaded');
});
autoUpdater.on('update-not-available', (info) => {
    mainWin.webContents.send('msg-to-preload', '無更新');
});
module.exports = {
    init: init,
    checkUpdate: checkUpdate,
    quitAndInstall: quitAndInstall,
}