<template>
  <select class="base basic-font" v-model="folderList.selected">
    <option class="basic-font" v-for="(option, index) in folderList.options" :key="index" v-bind:value="option.value">
      <span v-for="n in DirLength(option.value)" :key="n">■</span>
      {{ option.text }}
    </option>
  </select>
</template>

<style scoped>
.base {
  background-color: #0d2d50;
  border-radius: 3px;
  /* padding: 2px 4px; */
  -webkit-appearance: menulist !important; /* override vuetify style */
  -moze-appearance: menulist !important; /* override vuetify style */
  appearance: menulist !important; /* override vuetify style */
  /* font-weight: bold; */
  /* background-color: black; */
  color: rgb(252, 221, 44);
  outline: none;
}
.base option {
  font-size: 14px;
  background-color: #123c69;
  color: white;
}
</style>

<script>
import "@/assets/style.css";
export default {
  name: "VueDropdown",
  props: {
    folderList: Object,
    rootLength: Number,
    dirLengthCache: Object,
  },
  methods: {
    DirLength: function (dir) {
      if (this.rootLength > 0 && this.dirLengthCache && this.dirLengthCache[dir] !== undefined) {
        return this.dirLengthCache[dir] - this.rootLength;
      }
      return 0;
    },
  },
};
</script>
