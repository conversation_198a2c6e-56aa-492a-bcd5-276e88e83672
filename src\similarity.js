/**
 * 圖片相似度比較算法模組
 * 使用 Sharp 進行圖片處理和相似度比較
 */

const sharp = require('sharp');
const fs = require('fs');
const crypto = require('crypto');

class ImageSimilarityCalculator {
    constructor() {
        // 算法權重配置
        this.weights = {
            histogram: 0.4,
            pixel: 0.3,
            structure: 0.3
        };
    }

    /**
     * 計算圖片直方圖相似度
     */
    async calculateHistogramSimilarity(imagePath1, imagePath2) {
        try {
            // 將圖片轉換為灰度並調整大小
            const size = 64;
            const [hist1, hist2] = await Promise.all([
                this.getImageHistogram(imagePath1, size),
                this.getImageHistogram(imagePath2, size)
            ]);

            if (!hist1 || !hist2) {
                return 0;
            }

            // 計算直方圖相關係數
            const correlation = this.calculateCorrelation(hist1, hist2);
            return Math.max(0, correlation * 100);

        } catch (error) {
            console.error('直方圖計算錯誤:', error);
            return 0;
        }
    }

    /**
     * 獲取圖片直方圖
     */
    async getImageHistogram(imagePath, size) {
        try {
            const { data } = await sharp(imagePath)
                .resize(size, size)
                .greyscale()
                .raw()
                .toBuffer({ resolveWithObject: true });

            // 計算灰度直方圖 (256 個 bins)
            const histogram = new Array(256).fill(0);
            for (let i = 0; i < data.length; i++) {
                histogram[data[i]]++;
            }

            // 正規化
            const total = data.length;
            return histogram.map(count => count / total);

        } catch (error) {
            console.error('獲取直方圖錯誤:', error);
            return null;
        }
    }

    /**
     * 計算兩個直方圖的相關係數
     */
    calculateCorrelation(hist1, hist2) {
        const n = hist1.length;
        let sum1 = 0, sum2 = 0, sum1Sq = 0, sum2Sq = 0, pSum = 0;

        for (let i = 0; i < n; i++) {
            sum1 += hist1[i];
            sum2 += hist2[i];
            sum1Sq += hist1[i] * hist1[i];
            sum2Sq += hist2[i] * hist2[i];
            pSum += hist1[i] * hist2[i];
        }

        const num = pSum - (sum1 * sum2 / n);
        const den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));

        if (den === 0) return 0;
        return num / den;
    }

    /**
     * 計算像素級相似度
     */
    async calculatePixelSimilarity(imagePath1, imagePath2) {
        try {
            const size = 32; // 使用較小的尺寸提高速度

            const [data1, data2] = await Promise.all([
                sharp(imagePath1).resize(size, size).greyscale().raw().toBuffer(),
                sharp(imagePath2).resize(size, size).greyscale().raw().toBuffer()
            ]);

            if (data1.length !== data2.length) {
                return 0;
            }

            let totalDiff = 0;
            for (let i = 0; i < data1.length; i++) {
                totalDiff += Math.abs(data1[i] - data2[i]);
            }

            // 計算平均差異並轉換為相似度
            const avgDiff = totalDiff / data1.length;
            const similarity = Math.max(0, (255 - avgDiff) / 255 * 100);

            return similarity;
        } catch (error) {
            console.error('像素比較錯誤:', error);
            return 0;
        }
    }

    /**
     * 計算結構相似性 (簡化版 SSIM)
     */
    async calculateStructuralSimilarity(imagePath1, imagePath2) {
        try {
            const size = 32;

            const [data1, data2] = await Promise.all([
                sharp(imagePath1).resize(size, size).greyscale().raw().toBuffer(),
                sharp(imagePath2).resize(size, size).greyscale().raw().toBuffer()
            ]);

            if (data1.length !== data2.length) {
                return 0;
            }

            const totalPixels = data1.length;

            // 計算均值
            let sum1 = 0, sum2 = 0;
            for (let i = 0; i < totalPixels; i++) {
                sum1 += data1[i];
                sum2 += data2[i];
            }
            const mean1 = sum1 / totalPixels;
            const mean2 = sum2 / totalPixels;

            // 計算方差和協方差
            let var1 = 0, var2 = 0, covar = 0;
            for (let i = 0; i < totalPixels; i++) {
                const diff1 = data1[i] - mean1;
                const diff2 = data2[i] - mean2;

                var1 += diff1 * diff1;
                var2 += diff2 * diff2;
                covar += diff1 * diff2;
            }

            var1 /= totalPixels;
            var2 /= totalPixels;
            covar /= totalPixels;

            // 簡化的 SSIM 計算
            const c1 = 6.5025; // (0.01 * 255)^2
            const c2 = 58.5225; // (0.03 * 255)^2

            const numerator = (2 * mean1 * mean2 + c1) * (2 * covar + c2);
            const denominator = (mean1 * mean1 + mean2 * mean2 + c1) * (var1 + var2 + c2);

            if (denominator === 0) return 0;
            const ssim = numerator / denominator;
            return Math.max(0, ssim * 100);

        } catch (error) {
            console.error('結構相似性計算錯誤:', error);
            return 0;
        }
    }

    /**
     * 計算兩張圖片的綜合相似度
     */
    async calculateSimilarity(imagePath1, imagePath2, customWeights) {
        try {
            // 檢查檔案是否存在
            if (!fs.existsSync(imagePath1) || !fs.existsSync(imagePath2)) {
                console.warn(`圖片檔案不存在: ${imagePath1} 或 ${imagePath2}`);
                return {
                    overall_similarity: 0,
                    histogram_similarity: 0,
                    pixel_similarity: 0,
                    structural_similarity: 0,
                    error: '檔案不存在'
                };
            }

            // 並行計算各種相似度
            const [histogramScore, pixelScore, structuralScore] = await Promise.all([
                this.calculateHistogramSimilarity(imagePath1, imagePath2),
                this.calculatePixelSimilarity(imagePath1, imagePath2),
                this.calculateStructuralSimilarity(imagePath1, imagePath2)
            ]);

            // 計算加權平均
            const weights = customWeights || this.weights;
            const overallScore = (
                histogramScore * weights.histogram +
                pixelScore * weights.pixel +
                structuralScore * weights.structure
            );

            return {
                overall_similarity: Math.round(overallScore * 100) / 100,
                histogram_similarity: Math.round(histogramScore * 100) / 100,
                pixel_similarity: Math.round(pixelScore * 100) / 100,
                structural_similarity: Math.round(structuralScore * 100) / 100,
                algorithm_weights: weights
            };

        } catch (error) {
            console.error('相似度計算錯誤:', error);
            return {
                overall_similarity: 0,
                histogram_similarity: 0,
                pixel_similarity: 0,
                structural_similarity: 0,
                error: error.message
            };
        }
    }
}

module.exports = ImageSimilarityCalculator;
